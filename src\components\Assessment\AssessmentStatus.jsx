import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

const AssessmentStatus = () => {
  const { jobId } = useParams();
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-lg p-8 text-center"
        >
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Assessment Status
          </h1>
          
          <p className="text-gray-600 mb-6">
            Job ID: {jobId}
          </p>
          
          <p className="text-gray-600 mb-8">
            Halaman ini akan menampilkan status pemrosesan assessment Anda.
          </p>
          
          <button
            onClick={() => navigate('/assessment')}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Kembali ke Assessment
          </button>
        </motion.div>
      </div>
    </div>
  );
};

export default AssessmentStatus;
