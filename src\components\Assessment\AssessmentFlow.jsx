import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { viaQuestions, riasecQuestions, bigFiveQuestions } from '../../data/assessmentQuestions';

const AssessmentFlow = () => {
  const [selectedAssessment, setSelectedAssessment] = useState(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [isCompleted, setIsCompleted] = useState(false);

  // Available assessments
  const assessments = [
    {
      id: 'via',
      title: 'VIA Character Strengths',
      description: 'Temukan kekuatan karakter utama Anda',
      data: viaQuestions,
      color: 'blue'
    },
    {
      id: 'riasec',
      title: 'RIASEC Holland Codes',
      description: 'Eksplorasi minat karier Anda',
      data: riasecQuestions,
      color: 'green'
    },
    {
      id: 'bigfive',
      title: 'Big Five Personality',
      description: 'Analisis dimensi kepribadian Anda',
      data: bigFiveQuestions,
      color: 'purple'
    }
  ];

  // Load saved data from localStorage
  useEffect(() => {
    const savedAssessment = localStorage.getItem('currentAssessment');
    const savedAnswers = localStorage.getItem('assessmentAnswers');
    const savedQuestionIndex = localStorage.getItem('currentQuestionIndex');

    if (savedAssessment) {
      setSelectedAssessment(savedAssessment);
    }
    if (savedAnswers) {
      setAnswers(JSON.parse(savedAnswers));
    }
    if (savedQuestionIndex) {
      setCurrentQuestionIndex(parseInt(savedQuestionIndex));
    }
  }, []);

  // Save data to localStorage whenever state changes
  useEffect(() => {
    if (selectedAssessment) {
      localStorage.setItem('currentAssessment', selectedAssessment);
    }
  }, [selectedAssessment]);

  useEffect(() => {
    localStorage.setItem('assessmentAnswers', JSON.stringify(answers));
  }, [answers]);

  useEffect(() => {
    localStorage.setItem('currentQuestionIndex', currentQuestionIndex.toString());
  }, [currentQuestionIndex]);

  const handleAssessmentSelect = (assessmentId) => {
    setSelectedAssessment(assessmentId);
    setCurrentQuestionIndex(0);
    setAnswers({});
    setIsCompleted(false);
  };

  const handleAnswer = (questionIndex, answer) => {
    const newAnswers = {
      ...answers,
      [questionIndex]: answer
    };
    setAnswers(newAnswers);
  };

  const handleNext = () => {
    const assessment = assessments.find(a => a.id === selectedAssessment);
    const allQuestions = getAllQuestions(assessment.data);
    
    if (currentQuestionIndex < allQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      setIsCompleted(true);
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const getAllQuestions = (assessmentData) => {
    const questions = [];
    Object.values(assessmentData.categories).forEach(category => {
      category.questions.forEach((question, index) => {
        questions.push({
          text: question,
          category: category.name,
          index: questions.length
        });
      });
      // Add reverse questions if they exist
      if (category.reverseQuestions) {
        category.reverseQuestions.forEach((question, index) => {
          questions.push({
            text: question,
            category: category.name,
            index: questions.length,
            isReverse: true
          });
        });
      }
    });
    return questions;
  };

  const resetAssessment = () => {
    setSelectedAssessment(null);
    setCurrentQuestionIndex(0);
    setAnswers({});
    setIsCompleted(false);
    localStorage.removeItem('currentAssessment');
    localStorage.removeItem('assessmentAnswers');
    localStorage.removeItem('currentQuestionIndex');
  };

  const getColorClasses = (color) => {
    const colors = {
      blue: {
        bg: 'bg-blue-50',
        border: 'border-blue-200',
        text: 'text-blue-700',
        button: 'bg-blue-600 hover:bg-blue-700'
      },
      green: {
        bg: 'bg-green-50',
        border: 'border-green-200',
        text: 'text-green-700',
        button: 'bg-green-600 hover:bg-green-700'
      },
      purple: {
        bg: 'bg-purple-50',
        border: 'border-purple-200',
        text: 'text-purple-700',
        button: 'bg-purple-600 hover:bg-purple-700'
      }
    };
    return colors[color] || colors.blue;
  };

  // Assessment Selection Screen
  if (!selectedAssessment) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Pilih Assessment
            </h1>
            <p className="text-gray-600 text-lg">
              Pilih salah satu assessment untuk memulai perjalanan penemuan diri Anda
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-6">
            {assessments.map((assessment, index) => {
              const colorClasses = getColorClasses(assessment.color);
              return (
                <motion.div
                  key={assessment.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`${colorClasses.bg} ${colorClasses.border} border rounded-xl p-6 cursor-pointer hover:shadow-lg transition-all duration-300`}
                  onClick={() => handleAssessmentSelect(assessment.id)}
                >
                  <h3 className={`text-xl font-semibold ${colorClasses.text} mb-3`}>
                    {assessment.title}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {assessment.description}
                  </p>
                  <div className={`inline-flex items-center ${colorClasses.text} font-medium`}>
                    Mulai Assessment
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>
    );
  }

  const currentAssessment = assessments.find(a => a.id === selectedAssessment);
  const allQuestions = getAllQuestions(currentAssessment.data);
  const currentQuestion = allQuestions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / allQuestions.length) * 100;
  const colorClasses = getColorClasses(currentAssessment.color);

  // Completion Screen
  if (isCompleted) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4">
        <div className="max-w-2xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl shadow-lg p-8"
          >
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Assessment Selesai!
            </h2>
            <p className="text-gray-600 mb-8">
              Terima kasih telah menyelesaikan {currentAssessment.title}. 
              Jawaban Anda telah tersimpan di browser.
            </p>
            <div className="space-y-4">
              <button
                onClick={resetAssessment}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
              >
                Kembali ke Pilihan Assessment
              </button>
              <div className="text-sm text-gray-500">
                Total pertanyaan dijawab: {Object.keys(answers).length} dari {allQuestions.length}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  // Question Screen
  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-3xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-900">
              {currentAssessment.title}
            </h1>
            <button
              onClick={resetAssessment}
              className="text-gray-500 hover:text-gray-700 text-sm"
            >
              Ganti Assessment
            </button>
          </div>
          
          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <motion.div
              className={`h-2 rounded-full ${colorClasses.button.replace('hover:', '')}`}
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.3 }}
            />
          </div>
          
          <div className="flex justify-between text-sm text-gray-600">
            <span>Pertanyaan {currentQuestionIndex + 1} dari {allQuestions.length}</span>
            <span>{Math.round(progress)}% selesai</span>
          </div>
        </div>

        {/* Question Card */}
        <motion.div
          key={currentQuestionIndex}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white rounded-xl shadow-lg p-8 mb-8"
        >
          <div className="mb-6">
            <div className="text-sm text-gray-500 mb-2">
              Kategori: {currentQuestion.category}
            </div>
            <h2 className="text-xl font-medium text-gray-900 leading-relaxed">
              {currentQuestion.text}
            </h2>
          </div>

          {/* Answer Options */}
          <div className="space-y-3">
            {currentAssessment.data.scale.map((option) => (
              <label
                key={option.value}
                className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                  answers[currentQuestionIndex] === option.value
                    ? `${colorClasses.border} ${colorClasses.bg}`
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <input
                  type="radio"
                  name={`question-${currentQuestionIndex}`}
                  value={option.value}
                  checked={answers[currentQuestionIndex] === option.value}
                  onChange={(e) => handleAnswer(currentQuestionIndex, parseInt(e.target.value))}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${
                  answers[currentQuestionIndex] === option.value
                    ? `${colorClasses.border.replace('border-', 'border-')} ${colorClasses.bg}`
                    : 'border-gray-300'
                }`}>
                  {answers[currentQuestionIndex] === option.value && (
                    <div className={`w-2 h-2 rounded-full ${colorClasses.button.replace('bg-', 'bg-').replace(' hover:bg-blue-700', '')}`} />
                  )}
                </div>
                <span className="text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        </motion.div>

        {/* Navigation */}
        <div className="flex justify-between">
          <button
            onClick={handlePrevious}
            disabled={currentQuestionIndex === 0}
            className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Sebelumnya
          </button>
          
          <button
            onClick={handleNext}
            disabled={!answers[currentQuestionIndex]}
            className={`px-6 py-3 ${colorClasses.button} text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors`}
          >
            {currentQuestionIndex === allQuestions.length - 1 ? 'Selesai' : 'Selanjutnya'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AssessmentFlow;
