import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { viaQuestions, riasecQuestions, bigFiveQuestions } from '../../data/assessmentQuestions';
import { transformAssessmentScores } from '../../utils/assessmentTransformers';
import apiService from '../../services/apiService';
import { ChevronLeft, ChevronRight, Save, Send, RotateCcw } from 'lucide-react';

const AssessmentFlow = () => {
  const navigate = useNavigate();

  // State untuk assessment gabungan
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load data dari localStorage saat komponen mount
  useEffect(() => {
    loadFromLocalStorage();
  }, []);

  // Save ke localStorage setiap kali answers berubah
  useEffect(() => {
    saveToLocalStorage();
  }, [answers, currentQuestionIndex]);

  const STORAGE_KEY = 'ai_talent_mapping_assessment_progress';

  const loadFromLocalStorage = () => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const data = JSON.parse(saved);
        setAnswers(data.answers || {});
        setCurrentQuestionIndex(data.currentQuestionIndex || 0);
      }
    } catch (error) {
      console.error('Error loading from localStorage:', error);
    }
  };

  const saveToLocalStorage = () => {
    try {
      const data = {
        answers,
        currentQuestionIndex,
        totalQuestions: allQuestions.length,
        lastUpdated: new Date().toISOString(),
        version: '1.0'
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  };

  // Fungsi untuk mendapatkan semua pertanyaan dalam format flat dari ketiga assessment
  const getAllQuestions = () => {
    const questions = [];

    // VIA Questions
    Object.entries(viaQuestions.categories).forEach(([categoryKey, category]) => {
      category.questions.forEach((question, index) => {
        questions.push({
          id: `via_${categoryKey}_${index}`,
          text: question,
          category: categoryKey,
          categoryName: category.name,
          assessmentType: 'via',
          scale: viaQuestions.scale,
          isReverse: false
        });
      });
    });

    // RIASEC Questions
    Object.entries(riasecQuestions.categories).forEach(([categoryKey, category]) => {
      category.questions.forEach((question, index) => {
        questions.push({
          id: `riasec_${categoryKey}_${index}`,
          text: question,
          category: categoryKey,
          categoryName: category.name,
          assessmentType: 'riasec',
          scale: riasecQuestions.scale,
          isReverse: false
        });
      });
    });

    // Big Five Questions
    Object.entries(bigFiveQuestions.categories).forEach(([categoryKey, category]) => {
      // Regular questions
      if (category.questions) {
        category.questions.forEach((question, index) => {
          questions.push({
            id: `bigFive_${categoryKey}_${index}`,
            text: question,
            category: categoryKey,
            categoryName: category.name,
            assessmentType: 'bigFive',
            scale: bigFiveQuestions.scale,
            isReverse: false
          });
        });
      }

      // Reverse questions
      if (category.reverseQuestions) {
        category.reverseQuestions.forEach((question, index) => {
          questions.push({
            id: `bigFive_${categoryKey}_reverse_${index}`,
            text: question,
            category: categoryKey,
            categoryName: category.name,
            assessmentType: 'bigFive',
            scale: bigFiveQuestions.scale,
            isReverse: true
          });
        });
      }
    });

    return questions;
  };

  const allQuestions = getAllQuestions();
  const currentQuestion = allQuestions[currentQuestionIndex];

  // Fungsi untuk menangani jawaban
  const handleAnswer = (value) => {
    if (!currentQuestion) return;

    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: value
    }));
  };

  // Fungsi navigasi
  const goToNext = () => {
    if (currentQuestionIndex < allQuestions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const goToPrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  // Fungsi untuk reset assessment
  const resetAssessment = () => {
    setCurrentQuestionIndex(0);
    setAnswers({});
    localStorage.removeItem(STORAGE_KEY);
  };

  // Fungsi untuk menghitung progress
  const getProgress = () => {
    if (!allQuestions.length) return 0;
    const answered = Object.keys(answers).length;
    return Math.round((answered / allQuestions.length) * 100);
  };

  // Fungsi untuk mengecek apakah assessment sudah selesai
  const isAssessmentComplete = () => {
    return Object.keys(answers).length === allQuestions.length;
  };

  // Fungsi untuk menghitung skor per kategori
  const calculateCategoryScores = () => {
    const viaScores = {};
    const riasecScores = {};
    const bigFiveScores = {};

    // Hitung skor VIA
    Object.entries(viaQuestions.categories).forEach(([categoryKey, category]) => {
      const categoryAnswers = [];
      category.questions.forEach((_, index) => {
        const questionId = `via_${categoryKey}_${index}`;
        if (answers[questionId]) {
          categoryAnswers.push(answers[questionId]);
        }
      });

      if (categoryAnswers.length > 0) {
        const average = categoryAnswers.reduce((sum, val) => sum + val, 0) / categoryAnswers.length;
        viaScores[categoryKey] = Math.round((average / 7) * 100); // Convert to 0-100 scale
      }
    });

    // Hitung skor RIASEC
    Object.entries(riasecQuestions.categories).forEach(([categoryKey, category]) => {
      const categoryAnswers = [];
      category.questions.forEach((_, index) => {
        const questionId = `riasec_${categoryKey}_${index}`;
        if (answers[questionId]) {
          categoryAnswers.push(answers[questionId]);
        }
      });

      if (categoryAnswers.length > 0) {
        const average = categoryAnswers.reduce((sum, val) => sum + val, 0) / categoryAnswers.length;
        riasecScores[categoryKey] = Math.round((average / 7) * 100); // Convert to 0-100 scale
      }
    });

    // Hitung skor Big Five
    Object.entries(bigFiveQuestions.categories).forEach(([categoryKey, category]) => {
      const categoryAnswers = [];

      // Regular questions
      if (category.questions) {
        category.questions.forEach((_, index) => {
          const questionId = `bigFive_${categoryKey}_${index}`;
          if (answers[questionId]) {
            categoryAnswers.push(answers[questionId]);
          }
        });
      }

      // Reverse questions (perlu di-reverse)
      if (category.reverseQuestions) {
        category.reverseQuestions.forEach((_, index) => {
          const questionId = `bigFive_${categoryKey}_reverse_${index}`;
          if (answers[questionId]) {
            categoryAnswers.push(8 - answers[questionId]); // Reverse scale 1-7 to 7-1
          }
        });
      }

      if (categoryAnswers.length > 0) {
        const average = categoryAnswers.reduce((sum, val) => sum + val, 0) / categoryAnswers.length;
        bigFiveScores[categoryKey] = Math.round((average / 7) * 100); // Convert to 0-100 scale
      }
    });

    return { via: viaScores, riasec: riasecScores, bigFive: bigFiveScores };
  };

  // Fungsi untuk submit assessment
  const submitAssessment = async () => {
    if (!isAssessmentComplete()) {
      alert('Mohon lengkapi semua pertanyaan terlebih dahulu.');
      return;
    }

    setIsSubmitting(true);
    try {
      const categoryScores = calculateCategoryScores();
      const transformedData = transformAssessmentScores(categoryScores);

      const response = await apiService.submitAssessment(transformedData);

      // Clear localStorage setelah berhasil submit
      localStorage.removeItem('assessment_progress');

      // Redirect ke status page atau results
      if (response.jobId) {
        navigate(`/assessment/status/${response.jobId}`);
      } else {
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('Error submitting assessment:', error);
      alert('Terjadi kesalahan saat mengirim assessment. Silakan coba lagi.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Jika belum ada pertanyaan, tampilkan intro
  if (allQuestions.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 py-8 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-3xl font-bold text-slate-900 mb-4">Loading Assessment...</h1>
        </div>
      </div>
    );
  }

  // Jika assessment belum dimulai, tampilkan intro
  if (currentQuestionIndex === 0 && Object.keys(answers).length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <h1 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              AI-Driven Talent Mapping Assessment
            </h1>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto mb-8">
              Assessment ini menggabungkan tiga dimensi penting untuk pemetaan talenta yang komprehensif.
              Progress Anda akan tersimpan otomatis sehingga Anda dapat melanjutkan kapan saja.
            </p>
          </motion.div>

          {/* Assessment Overview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-xl shadow-sm border border-slate-200 p-8 mb-8"
          >
            <h2 className="text-2xl font-semibold text-slate-900 mb-6 text-center">
              Apa yang akan Anda kerjakan?
            </h2>

            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-blue-600">96</span>
                </div>
                <h3 className="font-semibold text-slate-900 mb-2">VIA Character Strengths</h3>
                <p className="text-sm text-slate-600">Mengidentifikasi 24 kekuatan karakter Anda</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-green-600">60</span>
                </div>
                <h3 className="font-semibold text-slate-900 mb-2">RIASEC Holland Codes</h3>
                <p className="text-sm text-slate-600">Mengeksplorasi 6 dimensi minat karir Anda</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-purple-600">44</span>
                </div>
                <h3 className="font-semibold text-slate-900 mb-2">Big Five Inventory</h3>
                <p className="text-sm text-slate-600">Menganalisis 5 dimensi kepribadian Anda</p>
              </div>
            </div>

            <div className="text-center">
              <p className="text-slate-600 mb-6">
                <strong>Total: {allQuestions.length} pertanyaan</strong> • Estimasi waktu: 15-20 menit
              </p>

              <button
                onClick={() => setCurrentQuestionIndex(0)}
                className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all font-medium text-lg"
              >
                Mulai Assessment
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
          </motion.div>

          {/* Progress indicator jika ada jawaban tersimpan */}
          {Object.keys(answers).length > 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="bg-amber-50 border border-amber-200 rounded-xl p-6 mb-8"
            >
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="font-semibold text-amber-800">Progress Tersimpan</h3>
                  <p className="text-amber-700">Anda telah menjawab {Object.keys(answers).length} dari {allQuestions.length} pertanyaan</p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-amber-800">{getProgress()}%</div>
                  <div className="text-sm text-amber-600">Selesai</div>
                </div>
              </div>

              <div className="w-full bg-amber-200 rounded-full h-3 mb-4">
                <div
                  className="h-3 rounded-full bg-gradient-to-r from-amber-500 to-orange-500 transition-all duration-300"
                  style={{ width: `${getProgress()}%` }}
                />
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setCurrentQuestionIndex(Object.keys(answers).length)}
                  className="flex-1 py-2 px-4 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors font-medium"
                >
                  Lanjutkan dari pertanyaan #{Object.keys(answers).length + 1}
                </button>
                <button
                  onClick={resetAssessment}
                  className="px-4 py-2 border border-amber-300 text-amber-700 rounded-lg hover:bg-amber-100 transition-colors"
                >
                  <RotateCcw className="w-4 h-4" />
                </button>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    );
  }

  // Jika sedang mengerjakan assessment
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 py-8 px-4">
      <div className="max-w-3xl mx-auto">
        {/* Header dengan progress */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-sm border border-slate-200 p-6 mb-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-slate-900">
                AI-Driven Talent Mapping Assessment
              </h1>
              <p className="text-slate-600">
                Pertanyaan {currentQuestionIndex + 1} dari {allQuestions.length}
              </p>
            </div>
            <button
              onClick={() => navigate('/dashboard')}
              className="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors"
            >
              Kembali
            </button>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-slate-200 rounded-full h-3">
            <div
              className="h-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-300"
              style={{ width: `${((currentQuestionIndex + 1) / allQuestions.length) * 100}%` }}
            />
          </div>
          <div className="flex justify-between text-sm text-slate-600 mt-2">
            <span>Progress: {getProgress()}% jawaban tersimpan</span>
            <span>{Math.round(((currentQuestionIndex + 1) / allQuestions.length) * 100)}% selesai</span>
          </div>
        </motion.div>

        {/* Question Card */}
        <AnimatePresence mode="wait">
          {currentQuestion && (
            <motion.div
              key={currentQuestion.id}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="bg-white rounded-xl shadow-sm border border-slate-200 p-8"
            >
              {/* Assessment Type & Category Badge */}
              <div className="flex items-center gap-2 mb-6">
                <div className={`inline-block px-3 py-1 text-sm rounded-full ${
                  currentQuestion.assessmentType === 'via' ? 'bg-blue-100 text-blue-700' :
                  currentQuestion.assessmentType === 'riasec' ? 'bg-green-100 text-green-700' :
                  'bg-purple-100 text-purple-700'
                }`}>
                  {currentQuestion.assessmentType === 'via' ? 'VIA Character Strengths' :
                   currentQuestion.assessmentType === 'riasec' ? 'RIASEC Holland Codes' :
                   'Big Five Inventory'}
                </div>
                <div className="inline-block px-3 py-1 bg-slate-100 text-slate-700 text-sm rounded-full">
                  {currentQuestion.categoryName}
                </div>
              </div>

              {/* Question */}
              <h2 className="text-xl font-semibold text-slate-900 mb-8 leading-relaxed">
                {currentQuestion.text}
              </h2>

              {/* Scale */}
              <div className="space-y-3 mb-8">
                {currentQuestion.scale.map((option) => (
                  <label
                    key={option.value}
                    className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all ${
                      answers[currentQuestion.id] === option.value
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-slate-200 hover:border-slate-300'
                    }`}
                  >
                    <input
                      type="radio"
                      name="answer"
                      value={option.value}
                      checked={answers[currentQuestion.id] === option.value}
                      onChange={() => handleAnswer(option.value)}
                      className="sr-only"
                    />
                    <div className={`w-5 h-5 rounded-full border-2 mr-4 flex items-center justify-center ${
                      answers[currentQuestion.id] === option.value
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-slate-300'
                    }`}>
                      {answers[currentQuestion.id] === option.value && (
                        <div className="w-2 h-2 rounded-full bg-white" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-slate-900">{option.label}</span>
                        <span className="text-slate-500 text-sm">{option.value}</span>
                      </div>
                    </div>
                  </label>
                ))}
              </div>

              {/* Navigation */}
              <div className="flex justify-between items-center">
                <button
                  onClick={goToPrevious}
                  disabled={currentQuestionIndex === 0}
                  className="inline-flex items-center gap-2 px-6 py-3 text-slate-600 hover:text-slate-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <ChevronLeft className="w-4 h-4" />
                  Sebelumnya
                </button>

                <div className="flex items-center gap-2 text-sm text-slate-500">
                  <Save className="w-4 h-4" />
                  Tersimpan otomatis
                </div>

                {/* Tombol Next atau Submit */}
                {currentQuestionIndex === allQuestions.length - 1 ? (
                  <button
                    onClick={submitAssessment}
                    disabled={!isAssessmentComplete() || isSubmitting}
                    className="inline-flex items-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Mengirim...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4" />
                        Kirim Assessment
                      </>
                    )}
                  </button>
                ) : (
                  <button
                    onClick={goToNext}
                    className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Selanjutnya
                    <ChevronRight className="w-4 h-4" />
                  </button>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default AssessmentFlow;
